import 'package:permission_handler/permission_handler.dart';
import 'package:telephony/telephony.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/expense_provider.dart';
import '../utils/constants.dart';
import 'sms_parser.dart';

class SmsService {
  static final Telephony telephony = Telephony.instance;
  static WidgetRef? _ref;

  // 로깅 메서드 (개발 중에만 사용)
  static void _log(String message) {
    // TODO: 프로덕션에서는 로깅 프레임워크 사용
    _log('[SMS Service] $message');
  }

  // Provider 참조 설정
  static void setRef(WidgetRef ref) {
    _ref = ref;
  }

  // SMS 권한 확인
  static Future<bool> checkSmsPermission() async {
    final status = await Permission.sms.status;
    return status.isGranted;
  }

  // SMS 권한 요청
  static Future<bool> requestSmsPermission() async {
    final status = await Permission.sms.request();
    return status.isGranted;
  }

  // SMS 수신 리스너 설정
  static Future<void> setupSmsListener() async {
    final hasPermission = await checkSmsPermission();
    if (!hasPermission) {
      final granted = await requestSmsPermission();
      if (!granted) {
        _log('SMS 권한이 거부되었습니다.');
        return;
      }
    }

    // SMS 수신 리스너 등록
    telephony.listenIncomingSms(
      onNewMessage: onSmsReceived,
      onBackgroundMessage: onBackgroundSmsReceived,
    );

    _log('SMS 리스너가 설정되었습니다.');
  }

  // SMS 수신 시 호출되는 함수
  static void onSmsReceived(SmsMessage message) {
    _log('새 SMS 수신: ${message.body}');
    final date = message.date != null
        ? DateTime.fromMillisecondsSinceEpoch(message.date!)
        : DateTime.now();
    _processSmsMessage(message.body ?? '', date);
  }

  // 백그라운드에서 SMS 수신 시 호출되는 함수
  static void onBackgroundSmsReceived(SmsMessage message) {
    _log('백그라운드 SMS 수신: ${message.body}');
    final date = message.date != null
        ? DateTime.fromMillisecondsSinceEpoch(message.date!)
        : DateTime.now();
    _processSmsMessage(message.body ?? '', date);
  }

  // SMS 메시지 처리
  static void _processSmsMessage(String message, DateTime receivedAt) {
    // 카드/은행 관련 메시지인지 확인
    if (_isPaymentMessage(message)) {
      _log('결제 메시지 감지: $message');

      // 지출 정보 파싱
      final expense = SmsParser.parseExpenseFromSms(message, receivedAt);
      if (expense != null && _ref != null) {
        // Provider를 통해 지출 추가
        _ref!.read(expenseProvider.notifier).addExpense(expense);
        _log('지출 추가됨: ${expense.shop} - ${expense.formattedAmount}');
      }
    }
  }

  // 결제 관련 메시지인지 확인
  static bool _isPaymentMessage(String message) {
    // 결제 키워드 또는 은행 키워드가 포함되어 있는지 확인
    final hasPaymentKeyword = SmsConstants.paymentKeywords.any(
      (keyword) => RegExp(keyword, caseSensitive: false).hasMatch(message),
    );
    final hasBankKeyword = SmsConstants.bankKeywords.any(
      (keyword) => message.contains(keyword),
    );

    return hasPaymentKeyword || hasBankKeyword;
  }

  // 기존 SMS 메시지 읽기 (초기 데이터 로드용)
  static Future<void> loadExistingSmsMessages() async {
    final hasPermission = await checkSmsPermission();
    if (!hasPermission) {
      _log('SMS 권한이 없어 기존 메시지를 읽을 수 없습니다.');
      return;
    }

    try {
      // 최근 30일간의 SMS 메시지 가져오기
      final messages = await telephony.getInboxSms(
        columns: [SmsColumn.ADDRESS, SmsColumn.BODY, SmsColumn.DATE],
        filter: SmsFilter.where(SmsColumn.DATE).greaterThan(
          DateTime.now()
              .subtract(Duration(days: SmsConstants.smsLoadDays))
              .millisecondsSinceEpoch
              .toString(),
        ),
        sortOrder: [OrderBy(SmsColumn.DATE, sort: Sort.DESC)],
      );

      _log('기존 SMS 메시지 ${messages.length}개 확인 중...');

      int processedCount = 0;
      for (final message in messages) {
        final body = message.body ?? '';
        final date = message.date != null
            ? DateTime.fromMillisecondsSinceEpoch(message.date!)
            : DateTime.now();

        if (_isPaymentMessage(body)) {
          final expense = SmsParser.parseExpenseFromSms(body, date);
          if (expense != null && _ref != null) {
            await _ref!.read(expenseProvider.notifier).addExpense(expense);
            processedCount++;
          }
        }
      }

      _log('기존 SMS에서 $processedCount개의 지출 내역을 추가했습니다.');
    } catch (e) {
      _log('기존 SMS 메시지 로드 중 오류: $e');
    }
  }

  // SMS 권한 상태 확인
  static Future<String> getPermissionStatus() async {
    final status = await Permission.sms.status;

    switch (status) {
      case PermissionStatus.granted:
        return '허용됨';
      case PermissionStatus.denied:
        return '거부됨';
      case PermissionStatus.restricted:
        return '제한됨';
      case PermissionStatus.limited:
        return '제한적 허용';
      case PermissionStatus.permanentlyDenied:
        return '영구 거부됨';
      default:
        return '알 수 없음';
    }
  }
}
