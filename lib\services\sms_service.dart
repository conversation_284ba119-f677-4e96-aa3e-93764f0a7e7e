import 'package:permission_handler/permission_handler.dart';
import 'package:telephony/telephony.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/expense_provider.dart';
import 'sms_parser.dart';

class SmsService {
  static final Telephony telephony = Telephony.instance;
  static WidgetRef? _ref;

  // Provider 참조 설정
  static void setRef(WidgetRef ref) {
    _ref = ref;
  }

  // SMS 권한 확인
  static Future<bool> checkSmsPermission() async {
    final status = await Permission.sms.status;
    return status.isGranted;
  }

  // SMS 권한 요청
  static Future<bool> requestSmsPermission() async {
    final status = await Permission.sms.request();
    return status.isGranted;
  }

  // SMS 수신 리스너 설정
  static Future<void> setupSmsListener() async {
    final hasPermission = await checkSmsPermission();
    if (!hasPermission) {
      final granted = await requestSmsPermission();
      if (!granted) {
        print('SMS 권한이 거부되었습니다.');
        return;
      }
    }

    // SMS 수신 리스너 등록
    telephony.listenIncomingSms(
      onNewMessage: onSmsReceived,
      onBackgroundMessage: onBackgroundSmsReceived,
    );

    print('SMS 리스너가 설정되었습니다.');
  }

  // SMS 수신 시 호출되는 함수
  static void onSmsReceived(SmsMessage message) {
    print('새 SMS 수신: ${message.body}');
    _processSmsMessage(message.body ?? '', message.date ?? DateTime.now());
  }

  // 백그라운드에서 SMS 수신 시 호출되는 함수
  static void onBackgroundSmsReceived(SmsMessage message) {
    print('백그라운드 SMS 수신: ${message.body}');
    _processSmsMessage(message.body ?? '', message.date ?? DateTime.now());
  }

  // SMS 메시지 처리
  static void _processSmsMessage(String message, DateTime receivedAt) {
    // 카드/은행 관련 메시지인지 확인
    if (_isPaymentMessage(message)) {
      print('결제 메시지 감지: $message');
      
      // 지출 정보 파싱
      final expense = SmsParser.parseExpenseFromSms(message, receivedAt);
      if (expense != null && _ref != null) {
        // Provider를 통해 지출 추가
        _ref!.read(expenseProvider.notifier).addExpense(expense);
        print('지출 추가됨: ${expense.shop} - ${expense.formattedAmount}');
      }
    }
  }

  // 결제 관련 메시지인지 확인
  static bool _isPaymentMessage(String message) {
    final paymentKeywords = [
      '승인', '결제', '출금', '사용',
      '카드', '체크카드', '신용카드',
      '원', '\\d+,\\d+', // 금액 패턴
    ];

    final bankKeywords = [
      '국민', '신한', '우리', '하나', '기업',
      '농협', '수협', 'KB', 'NH', 'IBK',
      '삼성', '현대', '롯데', 'BC카드',
    ];

    // 결제 키워드 또는 은행 키워드가 포함되어 있는지 확인
    final hasPaymentKeyword = paymentKeywords.any((keyword) => 
        RegExp(keyword, caseSensitive: false).hasMatch(message));
    final hasBankKeyword = bankKeywords.any((keyword) => 
        message.contains(keyword));

    return hasPaymentKeyword || hasBankKeyword;
  }

  // 기존 SMS 메시지 읽기 (초기 데이터 로드용)
  static Future<void> loadExistingSmsMessages() async {
    final hasPermission = await checkSmsPermission();
    if (!hasPermission) {
      print('SMS 권한이 없어 기존 메시지를 읽을 수 없습니다.');
      return;
    }

    try {
      // 최근 30일간의 SMS 메시지 가져오기
      final messages = await telephony.getInboxSms(
        columns: [SmsColumn.ADDRESS, SmsColumn.BODY, SmsColumn.DATE],
        filter: SmsFilter.where(SmsColumn.DATE)
            .greaterThan(DateTime.now().subtract(const Duration(days: 30)).millisecondsSinceEpoch.toString()),
        sortOrder: [OrderBy(SmsColumn.DATE, sort: Sort.DESC)],
      );

      print('기존 SMS 메시지 ${messages.length}개 확인 중...');

      int processedCount = 0;
      for (final message in messages) {
        final body = message.body ?? '';
        final date = message.date != null 
            ? DateTime.fromMillisecondsSinceEpoch(message.date!)
            : DateTime.now();

        if (_isPaymentMessage(body)) {
          final expense = SmsParser.parseExpenseFromSms(body, date);
          if (expense != null && _ref != null) {
            await _ref!.read(expenseProvider.notifier).addExpense(expense);
            processedCount++;
          }
        }
      }

      print('기존 SMS에서 $processedCount개의 지출 내역을 추가했습니다.');
    } catch (e) {
      print('기존 SMS 메시지 로드 중 오류: $e');
    }
  }

  // SMS 권한 상태 확인
  static Future<String> getPermissionStatus() async {
    final status = await Permission.sms.status;
    
    switch (status) {
      case PermissionStatus.granted:
        return '허용됨';
      case PermissionStatus.denied:
        return '거부됨';
      case PermissionStatus.restricted:
        return '제한됨';
      case PermissionStatus.limited:
        return '제한적 허용';
      case PermissionStatus.permanentlyDenied:
        return '영구 거부됨';
      default:
        return '알 수 없음';
    }
  }
}
