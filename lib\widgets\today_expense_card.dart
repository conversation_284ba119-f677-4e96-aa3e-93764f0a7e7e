import 'package:flutter/material.dart';
import '../utils/constants.dart';

/// 오늘 지출을 표시하는 메인 카드 위젯
class TodayExpenseCard extends StatelessWidget {
  final String amount;

  const TodayExpenseCard({
    super.key,
    required this.amount,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.cardPadding),
      decoration: BoxDecoration(
        color: AppConstants.backgroundColor,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(color: Colors.grey[300]!),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '오늘 지출',
            style: TextStyle(
              fontSize: AppConstants.bodyFontSize,
              color: AppConstants.subtitleColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            amount,
            style: const TextStyle(
              fontSize: AppConstants.amountFontSize,
              fontWeight: FontWeight.bold,
              color: AppConstants.textColor,
            ),
          ),
        ],
      ),
    );
  }
}
