import 'package:flutter/material.dart';

/// 앱 전체에서 사용되는 상수들
class AppConstants {
  // 앱 정보
  static const String appName = '지출관리';
  static const String appSubtitle = 'SMS 자동 가계부';
  static const String appVersion = '1.0.0';

  // 색상
  static const Color primaryColor = Colors.black;
  static const Color backgroundColor = Colors.white;
  static const Color textColor = Colors.black87;
  static const Color subtitleColor = Colors.grey;
  static const Color borderColor = Color(0xFFE0E0E0);

  // 크기
  static const double defaultPadding = 16.0;
  static const double cardPadding = 24.0;
  static const double borderRadius = 12.0;
  static const double iconSize = 20.0;
  static const double cardElevation = 2.0;

  // 애니메이션
  static const Duration animationDuration = Duration(milliseconds: 300);
  static const Duration splashDuration = Duration(seconds: 3);

  // SMS 관련
  static const int smsLoadDays = 30;
  static const int recentExpensesLimit = 5;
  static const double recentExpensesHeight = 400.0;

  // 폰트 크기
  static const double titleFontSize = 28.0;
  static const double headlineFontSize = 32.0;
  static const double bodyFontSize = 16.0;
  static const double captionFontSize = 12.0;
  static const double amountFontSize = 36.0;
}

/// 카테고리 관련 상수
class CategoryConstants {
  static const Map<String, String> categoryIcons = {
    '식음료': '🍽️',
    '교통': '🚗',
    '쇼핑': '🛍️',
    '의료': '🏥',
    '문화': '🎬',
    '기타': '💳',
  };

  static const Map<String, List<String>> categoryKeywords = {
    '식음료': [
      '스타벅스',
      '맥도날드',
      '버거킹',
      'kfc',
      '롯데리아',
      '서브웨이',
      '카페',
      '커피',
      '치킨',
      '피자',
      '음식',
      '식당',
      '레스토랑',
      '베이커리',
    ],
    '교통': [
      'gs칼텍스',
      'sk에너지',
      '현대오일뱅크',
      's-oil',
      '주유소',
      '지하철',
      '버스',
      '택시',
      '카카오택시',
      '우버',
      '교통카드',
    ],
    '쇼핑': [
      '이마트',
      '롯데마트',
      '홈플러스',
      '코스트코',
      '다이소',
      '올리브영',
      '왓슨스',
      '편의점',
      'gs25',
      'cu',
      '세븐일레븐',
      '미니스톱',
    ],
    '의료': ['병원', '의원', '클리닉', '약국', '한의원', '치과'],
    '문화': ['cgv', '롯데시네마', '메가박스', '영화관', '노래방', 'pc방', '게임', '도서관', '서점'],
  };
}

/// SMS 파싱 관련 상수
class SmsConstants {
  static const List<String> approvalKeywords = ['승인', '결제', '출금', '사용'];
  static const List<String> cancelKeywords = ['취소', '환불', '거절'];

  static const List<String> paymentKeywords = [
    '승인', '결제', '출금', '사용',
    '카드', '체크카드', '신용카드',
    '원', '\\d+,\\d+', // 금액 패턴
  ];

  static const List<String> bankKeywords = [
    '국민',
    '신한',
    '우리',
    '하나',
    '기업',
    '농협',
    '수협',
    'KB',
    'NH',
    'IBK',
    '삼성',
    '현대',
    '롯데',
    'BC카드',
  ];

  static const List<String> excludeWords = [
    '카드',
    '승인',
    '결제',
    '출금',
    '사용',
    '원',
    '금액',
    '은행',
    '체크',
  ];

  static const int smsLoadDays = 30;
}
