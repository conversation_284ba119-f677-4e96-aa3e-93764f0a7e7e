# 📄 SMS 자동 가계부 앱 요구사항 정의서

## 평가

앱 이름 : 지출관리

개발 플랫폼 : Flutter (Android 전용)

주요 목적 : SMS 문자를 자동으로 인식하여 사용자의 지춟 내용을 기록하고, 카테고리별 통계를 제공하는 자동 가계부 앱

수익 모델 : AdMob 딩 광고 (배너 광고) |

## 🌟 2. 해당 기능

### 2.1 문자 자동 수신 및 파싱

- Android SMS 권한 요청
- 카드사/은행 문자 수신 시 자동 파싱
- 문자 내용에서 다음 항목 추출:
  - 사용일시 (날짜)
  - 사용처 (상호명)
  - 금액
  - 승인 여부

### 2.2 카테고리 자동 분류

- 룰 기반 분류
  - 예: "스타벅스" → 식음료
  - 예: "GS칼텍스" → 교통
- 향후 수동 편집 기능 확장 가능

### 2.3 로컬 저장소 기본 DB (Hive)

- Hive 사용
- 데이터 항목:

```json
{
  "id": string,
  "date": DateTime,
  "shop": string,
  "amount": int,
  "category": string
}
```

### 2.4 통계 화면

- 월간 지춟 총액
- 카테고리별 비율 원형 차트
- 가장 많이 쓰는 것 Top 5

### 2.5 광고 노출

- 하단에 상시 띠 광고 노출 (AdMob)
- 광고 비노출 프리미엄 업그레이드는 고려하지 않음 (처음엔 무료 앱)

---

## 🧱 3. 기술 요구사항

| 항목          | 기술 / 라이브러리           |
| ------------- | --------------------------- |
| UI 프레임워크 | Flutter                     |
| 상태관리      | Riverpod or Provider        |
| DB            | Hive                        |
| 차트          | fl_chart                    |
| 광고          | AdMob                       |
| 문자 수신     | `telephony` or `sms` 패키지 |
| 날짜 처리     | intl 패키지                 |

---

## 🔐 4. 권한 및 보안

| 항목             | 내용                                     |
| ---------------- | ---------------------------------------- |
| 요청 권한        | RECEIVE_SMS, READ_SMS                    |
| 설명 방식        | 기본 화면에서 SMS 기능 설명 후 권한 요청 |
| 개인정보 전송    | 없음 (로컬 저장)                         |
| 서버 통신        | 없음                                     |
| Google Play 등록 | 권한 설명 포함 시 승인 가능함            |

---

## 📅 5. 화면 구성

### 5.1 홈 화면

- 오늘 날짜
- 오늘 지춟 총액
- 최근 지춟 리스트 (5개)

### 5.2 전체 내용

- 날짜별 지춟 목록
- 금액, 카테고리 표시
- 검색/필터 (상호명 or 카테고리)

### 5.3 통계

- 월간 총지출
- 카테고리별 원형 차트
- Top 5 지춟 항목

### 5.4 설정

- 권한 상태 확인
- 광고 노출 안내
- 앱 버전, 개발자 정보

---

## 📂 6. 후상 확장 계획 (v2)

| 기능           | 설명                        |
| -------------- | --------------------------- |
| 수동 지출 입력 | 문자 안 오는 경우 직접 입력 |
| Firebase 연동  | 데이터 백업 및 기기 동기화  |
| 음성 입력      | 음성으로 지출 등록          |

---

## ✅ 7. 개발 및 배포 정보

| 항목             | 내용                                      |
| ---------------- | ----------------------------------------- |
| 배포 대상        | Google Play (Android)                     |
| 테스트 환경      | Android 10 이상                           |
| 데이터 유지 정책 | 앱 삭제 시 데이터 삭제됨 (로컬 저장 방식) |
| 릴리즈 계획      | 1차 버전: 무료 + 광고 포함                |
