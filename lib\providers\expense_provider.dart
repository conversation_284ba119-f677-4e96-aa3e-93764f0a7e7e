import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';
import '../models/expense.dart';
import '../services/sms_parser.dart';

class ExpenseNotifier extends StateNotifier<List<Expense>> {
  late Box<Expense> _expenseBox;

  ExpenseNotifier() : super([]) {
    _initHive();
  }

  Future<void> _initHive() async {
    try {
      // Hive 어댑터 등록 (이미 등록되어 있으면 무시)
      if (!Hive.isAdapterRegistered(0)) {
        Hive.registerAdapter(ExpenseAdapter());
      }
      
      _expenseBox = await Hive.openBox<Expense>('expenses');
      _loadExpenses();
      
      // 샘플 데이터가 없으면 추가
      if (state.isEmpty) {
        await _addSampleData();
      }
    } catch (e) {
      print('Hive 초기화 오류: $e');
    }
  }

  void _loadExpenses() {
    final expenses = _expenseBox.values.toList();
    expenses.sort((a, b) => b.date.compareTo(a.date)); // 최신순 정렬
    state = expenses;
  }

  Future<void> _addSampleData() async {
    final sampleData = SmsParser.generateSampleData();
    for (final expense in sampleData) {
      await _expenseBox.add(expense);
    }
    _loadExpenses();
  }

  // 새 지출 추가
  Future<void> addExpense(Expense expense) async {
    try {
      await _expenseBox.add(expense);
      _loadExpenses();
    } catch (e) {
      print('지출 추가 오류: $e');
    }
  }

  // SMS에서 지출 파싱 후 추가
  Future<void> addExpenseFromSms(String message, DateTime receivedAt) async {
    final expense = SmsParser.parseExpenseFromSms(message, receivedAt);
    if (expense != null) {
      await addExpense(expense);
    }
  }

  // 지출 삭제
  Future<void> deleteExpense(String id) async {
    try {
      final expense = _expenseBox.values.firstWhere((e) => e.id == id);
      await expense.delete();
      _loadExpenses();
    } catch (e) {
      print('지출 삭제 오류: $e');
    }
  }

  // 오늘 지출 총액
  int get todayTotal {
    final today = DateTime.now();
    return state
        .where((expense) => 
            expense.date.year == today.year &&
            expense.date.month == today.month &&
            expense.date.day == today.day)
        .fold(0, (sum, expense) => sum + expense.amount);
  }

  // 이번 달 지출 총액
  int get thisMonthTotal {
    final now = DateTime.now();
    return state
        .where((expense) => 
            expense.date.year == now.year &&
            expense.date.month == now.month)
        .fold(0, (sum, expense) => sum + expense.amount);
  }

  // 평균 일일 지출 (이번 달 기준)
  int get averageDailyExpense {
    final now = DateTime.now();
    final thisMonthExpenses = state
        .where((expense) => 
            expense.date.year == now.year &&
            expense.date.month == now.month)
        .toList();
    
    if (thisMonthExpenses.isEmpty) return 0;
    
    final daysInMonth = DateTime(now.year, now.month + 1, 0).day;
    final currentDay = now.day;
    
    return (thisMonthTotal / currentDay).round();
  }

  // 최근 지출 (최대 5개)
  List<Expense> get recentExpenses {
    return state.take(5).toList();
  }

  // 카테고리별 지출 통계
  Map<String, int> get categoryStats {
    final now = DateTime.now();
    final thisMonthExpenses = state
        .where((expense) => 
            expense.date.year == now.year &&
            expense.date.month == now.month)
        .toList();
    
    final stats = <String, int>{};
    for (final expense in thisMonthExpenses) {
      stats[expense.category] = (stats[expense.category] ?? 0) + expense.amount;
    }
    
    return stats;
  }

  // 금액 포맷팅
  String formatAmount(int amount) {
    return '${amount.toString().replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    )}원';
  }
}

// Provider 정의
final expenseProvider = StateNotifierProvider<ExpenseNotifier, List<Expense>>((ref) {
  return ExpenseNotifier();
});

// 편의를 위한 추가 Provider들
final todayTotalProvider = Provider<int>((ref) {
  final notifier = ref.watch(expenseProvider.notifier);
  return notifier.todayTotal;
});

final thisMonthTotalProvider = Provider<int>((ref) {
  final notifier = ref.watch(expenseProvider.notifier);
  return notifier.thisMonthTotal;
});

final averageDailyExpenseProvider = Provider<int>((ref) {
  final notifier = ref.watch(expenseProvider.notifier);
  return notifier.averageDailyExpense;
});

final recentExpensesProvider = Provider<List<Expense>>((ref) {
  final notifier = ref.watch(expenseProvider.notifier);
  return notifier.recentExpenses;
});

final categoryStatsProvider = Provider<Map<String, int>>((ref) {
  final notifier = ref.watch(expenseProvider.notifier);
  return notifier.categoryStats;
});
