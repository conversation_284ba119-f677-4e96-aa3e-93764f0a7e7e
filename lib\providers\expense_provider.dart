import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';
import '../models/expense.dart';
import '../services/sms_parser.dart';
import '../utils/formatters.dart';

class ExpenseNotifier extends StateNotifier<List<Expense>> {
  late Box<Expense> _expenseBox;

  ExpenseNotifier() : super([]) {
    _initHive();
  }

  Future<void> _initHive() async {
    try {
      // Hive 어댑터 등록 (이미 등록되어 있으면 무시)
      if (!Hive.isAdapterRegistered(0)) {
        Hive.registerAdapter(ExpenseAdapter());
      }

      _expenseBox = await Hive.openBox<Expense>('expenses');
      _loadExpenses();
    } catch (e) {
      // TODO: 로깅 프레임워크 사용
      print('[ExpenseProvider] Hive 초기화 오류: $e');
    }
  }

  void _loadExpenses() {
    final expenses = _expenseBox.values.toList();
    expenses.sort((a, b) => b.date.compareTo(a.date)); // 최신순 정렬
    state = expenses;
  }

  // 새 지출 추가
  Future<void> addExpense(Expense expense) async {
    try {
      await _expenseBox.add(expense);
      _loadExpenses();
    } catch (e) {
      // TODO: 로깅 프레임워크 사용
      print('[ExpenseProvider] 지출 추가 오류: $e');
    }
  }

  // SMS에서 지출 파싱 후 추가
  Future<void> addExpenseFromSms(String message, DateTime receivedAt) async {
    final expense = SmsParser.parseExpenseFromSms(message, receivedAt);
    if (expense != null) {
      await addExpense(expense);
    }
  }

  // 지출 삭제
  Future<void> deleteExpense(String id) async {
    try {
      final expense = _expenseBox.values.firstWhere((e) => e.id == id);
      await expense.delete();
      _loadExpenses();
    } catch (e) {
      // TODO: 로깅 프레임워크 사용
      print('[ExpenseProvider] 지출 삭제 오류: $e');
    }
  }

  // 금액 포맷팅
  String formatAmount(int amount) {
    return Formatters.formatAmount(amount);
  }
}

// Provider 정의
final expenseProvider = StateNotifierProvider<ExpenseNotifier, List<Expense>>((
  ref,
) {
  return ExpenseNotifier();
});

// 편의를 위한 추가 Provider들
final todayTotalProvider = Provider<int>((ref) {
  final expenses = ref.watch(expenseProvider);
  final now = DateTime.now();
  final today = DateTime(now.year, now.month, now.day);
  final tomorrow = today.add(const Duration(days: 1));

  return expenses
      .where(
        (expense) =>
            expense.date.isAfter(today) && expense.date.isBefore(tomorrow),
      )
      .fold(0, (sum, expense) => sum + expense.amount);
});

final thisMonthTotalProvider = Provider<int>((ref) {
  final expenses = ref.watch(expenseProvider);
  final now = DateTime.now();
  final thisMonth = DateTime(now.year, now.month);
  final nextMonth = DateTime(now.year, now.month + 1);

  return expenses
      .where(
        (expense) =>
            expense.date.isAfter(thisMonth) && expense.date.isBefore(nextMonth),
      )
      .fold(0, (sum, expense) => sum + expense.amount);
});

final averageDailyExpenseProvider = Provider<int>((ref) {
  final expenses = ref.watch(expenseProvider);
  final now = DateTime.now();
  final thisMonth = DateTime(now.year, now.month);
  final nextMonth = DateTime(now.year, now.month + 1);

  final thisMonthExpenses = expenses
      .where(
        (expense) =>
            expense.date.isAfter(thisMonth) && expense.date.isBefore(nextMonth),
      )
      .toList();

  if (thisMonthExpenses.isEmpty) return 0;

  final thisMonthTotal = thisMonthExpenses.fold(
    0,
    (sum, expense) => sum + expense.amount,
  );
  final currentDay = now.day;

  return (thisMonthTotal / currentDay).round();
});

final recentExpensesProvider = Provider<List<Expense>>((ref) {
  final expenses = ref.watch(expenseProvider);
  return expenses.take(5).toList();
});

final categoryStatsProvider = Provider<Map<String, int>>((ref) {
  final expenses = ref.watch(expenseProvider);
  final Map<String, int> stats = {};

  for (final expense in expenses) {
    stats[expense.category] = (stats[expense.category] ?? 0) + expense.amount;
  }

  return stats;
});
