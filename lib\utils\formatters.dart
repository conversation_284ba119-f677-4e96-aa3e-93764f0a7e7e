/// 포맷팅 관련 유틸리티 함수들
class Formatters {
  /// 금액을 원화 형식으로 포맷
  static String formatAmount(int amount) {
    return '${amount.toString().replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    )}원';
  }

  /// 상대적 시간 포맷 (예: "2시간 전", "1일 전")
  static String formatRelativeTime(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}일 전';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}시간 전';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}분 전';
    } else {
      return '방금 전';
    }
  }

  /// 날짜를 "MM월 dd일" 형식으로 포맷
  static String formatDate(DateTime date) {
    return '${date.month}월 ${date.day}일';
  }

  /// 날짜를 "yyyy-MM-dd" 형식으로 포맷
  static String formatDateISO(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// 시간을 "HH:mm" 형식으로 포맷
  static String formatTime(DateTime date) {
    return '${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  /// 문자열에서 숫자만 추출
  static String extractNumbers(String text) {
    return text.replaceAll(RegExp(r'[^\d]'), '');
  }

  /// 문자열에서 콤마 제거
  static String removeCommas(String text) {
    return text.replaceAll(',', '');
  }

  /// 문자열을 정수로 안전하게 변환
  static int? safeParseInt(String text) {
    try {
      return int.parse(removeCommas(text));
    } catch (e) {
      return null;
    }
  }

  /// 텍스트 정리 (특수문자 제거, 공백 정리)
  static String cleanText(String text) {
    return text
        .replaceAll(RegExp(r'[^\w가-힣\s]'), '')
        .trim()
        .replaceAll(RegExp(r'\s+'), ' ');
  }

  /// 퍼센트 포맷
  static String formatPercentage(double value) {
    return '${value.toStringAsFixed(1)}%';
  }

  /// 큰 숫자를 K, M 단위로 축약
  static String formatCompactNumber(int number) {
    if (number >= 1000000) {
      return '${(number / 1000000).toStringAsFixed(1)}M';
    } else if (number >= 1000) {
      return '${(number / 1000).toStringAsFixed(1)}K';
    } else {
      return number.toString();
    }
  }
}
