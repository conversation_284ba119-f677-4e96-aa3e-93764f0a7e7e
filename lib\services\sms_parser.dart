import 'package:uuid/uuid.dart';
import '../models/expense.dart';

class SmsParser {
  static const _uuid = Uuid();

  // SMS 메시지에서 지출 정보 파싱
  static Expense? parseExpenseFromSms(String message, DateTime receivedAt) {
    try {
      // 승인 여부 확인
      if (!message.contains('승인') && !message.contains('결제')) {
        return null;
      }

      // 금액 추출 (숫자,숫자 형태 또는 숫자원 형태)
      final amountRegex = RegExp(r'(\d{1,3}(?:,\d{3})*)\s*원');
      final amountMatch = amountRegex.firstMatch(message);
      if (amountMatch == null) return null;

      final amountStr = amountMatch.group(1)!.replaceAll(',', '');
      final amount = int.tryParse(amountStr);
      if (amount == null || amount <= 0) return null;

      // 상호명 추출
      String shop = _extractShopName(message);

      // 카테고리 자동 분류
      String category = _categorizeExpense(shop, message);

      return Expense(
        id: _uuid.v4(),
        date: receivedAt,
        shop: shop,
        amount: amount,
        category: category,
        originalMessage: message,
        isApproved: true,
      );
    } catch (e) {
      print('SMS 파싱 오류: $e');
      return null;
    }
  }

  // 상호명 추출
  static String _extractShopName(String message) {
    // 일반적인 패턴들
    final patterns = [
      RegExp(r'(\S+)\s*에서\s*승인'),
      RegExp(r'(\S+)\s*승인'),
      RegExp(r'(\S+)\s*결제'),
      RegExp(r'\[(\S+)\]'),
      RegExp(r'(\S+)\s*\d{1,3}(?:,\d{3})*\s*원'),
    ];

    for (final pattern in patterns) {
      final match = pattern.firstMatch(message);
      if (match != null) {
        String shop = match.group(1)!;
        // 불필요한 문자 제거
        shop = shop.replaceAll(RegExp(r'[^\w가-힣\s]'), '').trim();
        if (shop.isNotEmpty && shop.length > 1) {
          return shop;
        }
      }
    }

    return '알 수 없음';
  }

  // 카테고리 자동 분류
  static String _categorizeExpense(String shop, String message) {
    final shopLower = shop.toLowerCase();
    final messageLower = message.toLowerCase();

    // 식음료
    if (_containsAny(shopLower, ['스타벅스', '맥도날드', '버거킹', 'kfc', '롯데리아', '서브웨이', 
                                '카페', '커피', '치킨', '피자', '음식', '식당', '레스토랑', '베이커리'])) {
      return '식음료';
    }

    // 교통
    if (_containsAny(shopLower, ['gs칼텍스', 'sk에너지', '현대오일뱅크', 's-oil', '주유소', 
                                '지하철', '버스', '택시', '카카오택시', '우버', '교통카드'])) {
      return '교통';
    }

    // 쇼핑
    if (_containsAny(shopLower, ['이마트', '롯데마트', '홈플러스', '코스트코', '다이소', 
                                '올리브영', '왓슨스', '편의점', 'gs25', 'cu', '세븐일레븐', '미니스톱'])) {
      return '쇼핑';
    }

    // 의료
    if (_containsAny(shopLower, ['병원', '의원', '클리닉', '약국', '한의원', '치과'])) {
      return '의료';
    }

    // 문화
    if (_containsAny(shopLower, ['cgv', '롯데시네마', '메가박스', '영화관', '노래방', 
                                'pc방', '게임', '도서관', '서점'])) {
      return '문화';
    }

    return '기타';
  }

  static bool _containsAny(String text, List<String> keywords) {
    return keywords.any((keyword) => text.contains(keyword));
  }

  // 테스트용 샘플 데이터 생성
  static List<Expense> generateSampleData() {
    final now = DateTime.now();
    return [
      Expense(
        id: _uuid.v4(),
        date: now,
        shop: '스타벅스 강남점',
        amount: 4500,
        category: '식음료',
        originalMessage: '[스타벅스 강남점] 4,500원 승인 (12/25 14:30)',
      ),
      Expense(
        id: _uuid.v4(),
        date: now.subtract(const Duration(hours: 2)),
        shop: 'GS25 편의점',
        amount: 2800,
        category: '쇼핑',
        originalMessage: '[GS25] 2,800원 승인 (12/25 12:15)',
      ),
      Expense(
        id: _uuid.v4(),
        date: now.subtract(const Duration(days: 1)),
        shop: '맥도날드',
        amount: 8900,
        category: '식음료',
        originalMessage: '[맥도날드] 8,900원 승인 (12/24 19:45)',
      ),
      Expense(
        id: _uuid.v4(),
        date: now.subtract(const Duration(days: 1, hours: 5)),
        shop: 'GS칼텍스 주유소',
        amount: 45000,
        category: '교통',
        originalMessage: '[GS칼텍스] 45,000원 승인 (12/24 14:20)',
      ),
      Expense(
        id: _uuid.v4(),
        date: now.subtract(const Duration(days: 2)),
        shop: '이마트',
        amount: 23400,
        category: '쇼핑',
        originalMessage: '[이마트] 23,400원 승인 (12/23 16:30)',
      ),
      Expense(
        id: _uuid.v4(),
        date: now.subtract(const Duration(days: 3)),
        shop: 'CGV 영화관',
        amount: 12000,
        category: '문화',
        originalMessage: '[CGV] 12,000원 승인 (12/22 20:15)',
      ),
      Expense(
        id: _uuid.v4(),
        date: now.subtract(const Duration(days: 5)),
        shop: '올리브영',
        amount: 15600,
        category: '쇼핑',
        originalMessage: '[올리브영] 15,600원 승인 (12/20 15:45)',
      ),
    ];
  }
}
