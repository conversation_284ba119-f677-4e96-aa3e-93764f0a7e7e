import 'package:uuid/uuid.dart';
import '../models/expense.dart';
import '../utils/constants.dart';
import '../utils/formatters.dart';

class SmsParser {
  static const _uuid = Uuid();

  // 로깅 메서드 (개발 중에만 사용)
  static void _log(String message) {
    // TODO: 프로덕션에서는 로깅 프레임워크 사용
    print('[SMS Parser] $message');
  }

  // SMS 메시지에서 지출 정보 파싱
  static Expense? parseExpenseFromSms(String message, DateTime receivedAt) {
    try {
      _log('SMS 파싱 시도: $message');

      // 취소, 환불 메시지는 제외
      final hasCancelKeyword = SmsConstants.cancelKeywords.any(
        (keyword) => message.contains(keyword),
      );
      if (hasCancelKeyword) {
        _log('취소/환불 메시지로 판단하여 제외');
        return null;
      }

      // 승인 또는 결제 관련 키워드 확인
      final hasApprovalKeyword = SmsConstants.approvalKeywords.any(
        (keyword) => message.contains(keyword),
      );

      if (!hasApprovalKeyword) {
        _log('승인/결제 키워드가 없어 제외');
        return null;
      }

      // 금액 추출 (다양한 패턴 지원)
      final amountPatterns = [
        RegExp(r'(\d{1,3}(?:,\d{3})*)\s*원'), // 1,000원
        RegExp(r'(\d+)\s*원'), // 1000원
        RegExp(r'금액\s*:?\s*(\d{1,3}(?:,\d{3})*)\s*원'), // 금액: 1,000원
        RegExp(r'(\d{1,3}(?:,\d{3})*)\s*KRW'), // 1,000KRW
      ];

      int? amount;
      for (final pattern in amountPatterns) {
        final match = pattern.firstMatch(message);
        if (match != null) {
          final amountStr = match.group(1)!.replaceAll(',', '');
          amount = int.tryParse(amountStr);
          if (amount != null && amount > 0) {
            _log('금액 추출 성공: $amount원');
            break;
          }
        }
      }

      if (amount == null || amount <= 0) {
        _log('유효한 금액을 찾을 수 없음');
        return null;
      }

      // 상호명 추출
      String shop = _extractShopName(message);
      _log('상호명 추출: $shop');

      // 카테고리 자동 분류
      String category = _categorizeExpense(shop, message);
      _log('카테고리 분류: $category');

      final expense = Expense(
        id: _uuid.v4(),
        date: receivedAt,
        shop: shop,
        amount: amount,
        category: category,
        originalMessage: message,
        isApproved: true,
      );

      _log('지출 파싱 성공: ${expense.shop} - ${expense.formattedAmount}');
      return expense;
    } catch (e) {
      _log('SMS 파싱 오류: $e');
      return null;
    }
  }

  // 상호명 추출
  static String _extractShopName(String message) {
    // 다양한 패턴으로 상호명 추출 시도
    final patterns = [
      // [상호명] 패턴
      RegExp(r'\[([^\]]+)\]'),
      // 상호명에서 승인 패턴
      RegExp(r'([가-힣a-zA-Z0-9\s]+)\s*에서\s*승인'),
      RegExp(r'([가-힣a-zA-Z0-9\s]+)\s*승인'),
      // 상호명 결제 패턴
      RegExp(r'([가-힣a-zA-Z0-9\s]+)\s*결제'),
      // 금액 앞의 상호명 패턴
      RegExp(r'([가-힣a-zA-Z0-9\s]+)\s*\d{1,3}(?:,\d{3})*\s*원'),
      // 카드사별 패턴
      RegExp(r'가맹점\s*:\s*([^\n\r]+)'),
      RegExp(r'사용처\s*:\s*([^\n\r]+)'),
      // 일반적인 상호명 패턴 (한글, 영문, 숫자 조합)
      RegExp(r'([가-힣]{2,}[가-힣a-zA-Z0-9\s]*[가-힣a-zA-Z0-9])'),
    ];

    for (final pattern in patterns) {
      final match = pattern.firstMatch(message);
      if (match != null) {
        String shop = match.group(1)!.trim();

        // 불필요한 문자 제거
        shop = shop.replaceAll(RegExp(r'[^\w가-힣\s]'), '').trim();

        // 너무 짧거나 숫자만 있는 경우 제외
        if (shop.length >= 2 && !RegExp(r'^\d+$').hasMatch(shop)) {
          // 일반적인 불필요한 단어 제거
          final excludeWords = ['카드', '승인', '결제', '출금', '사용', '원', '금액'];
          bool isValidShop = true;

          for (final word in excludeWords) {
            if (shop == word) {
              isValidShop = false;
              break;
            }
          }

          if (isValidShop) {
            return shop;
          }
        }
      }
    }

    // 패턴 매칭에 실패한 경우, 메시지에서 한글 상호명 추출 시도
    final koreanMatch = RegExp(r'([가-힣]{2,})').firstMatch(message);
    if (koreanMatch != null) {
      final shop = koreanMatch.group(1)!;
      final excludeWords = ['카드', '승인', '결제', '출금', '사용', '은행', '체크'];
      if (!excludeWords.contains(shop)) {
        return shop;
      }
    }

    return '알 수 없음';
  }

  // 카테고리 자동 분류
  static String _categorizeExpense(String shop, String message) {
    final shopLower = shop.toLowerCase();

    // 각 카테고리별로 키워드 확인
    for (final entry in CategoryConstants.categoryKeywords.entries) {
      if (_containsAny(shopLower, entry.value)) {
        return entry.key;
      }
    }

    return '기타';
  }

  static bool _containsAny(String text, List<String> keywords) {
    return keywords.any((keyword) => text.contains(keyword));
  }

  // 테스트용 샘플 데이터 생성
  static List<Expense> generateSampleData() {
    final now = DateTime.now();
    return [
      Expense(
        id: _uuid.v4(),
        date: now,
        shop: '스타벅스 강남점',
        amount: 4500,
        category: '식음료',
        originalMessage: '[스타벅스 강남점] 4,500원 승인 (12/25 14:30)',
      ),
      Expense(
        id: _uuid.v4(),
        date: now.subtract(const Duration(hours: 2)),
        shop: 'GS25 편의점',
        amount: 2800,
        category: '쇼핑',
        originalMessage: '[GS25] 2,800원 승인 (12/25 12:15)',
      ),
      Expense(
        id: _uuid.v4(),
        date: now.subtract(const Duration(days: 1)),
        shop: '맥도날드',
        amount: 8900,
        category: '식음료',
        originalMessage: '[맥도날드] 8,900원 승인 (12/24 19:45)',
      ),
      Expense(
        id: _uuid.v4(),
        date: now.subtract(const Duration(days: 1, hours: 5)),
        shop: 'GS칼텍스 주유소',
        amount: 45000,
        category: '교통',
        originalMessage: '[GS칼텍스] 45,000원 승인 (12/24 14:20)',
      ),
      Expense(
        id: _uuid.v4(),
        date: now.subtract(const Duration(days: 2)),
        shop: '이마트',
        amount: 23400,
        category: '쇼핑',
        originalMessage: '[이마트] 23,400원 승인 (12/23 16:30)',
      ),
      Expense(
        id: _uuid.v4(),
        date: now.subtract(const Duration(days: 3)),
        shop: 'CGV 영화관',
        amount: 12000,
        category: '문화',
        originalMessage: '[CGV] 12,000원 승인 (12/22 20:15)',
      ),
      Expense(
        id: _uuid.v4(),
        date: now.subtract(const Duration(days: 5)),
        shop: '올리브영',
        amount: 15600,
        category: '쇼핑',
        originalMessage: '[올리브영] 15,600원 승인 (12/20 15:45)',
      ),
    ];
  }
}
