import 'package:hive/hive.dart';

part 'expense.g.dart';

@HiveType(typeId: 0)
class Expense extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  DateTime date;

  @HiveField(2)
  String shop;

  @HiveField(3)
  int amount;

  @HiveField(4)
  String category;

  @HiveField(5)
  String? originalMessage;

  @HiveField(6)
  bool isApproved;

  Expense({
    required this.id,
    required this.date,
    required this.shop,
    required this.amount,
    required this.category,
    this.originalMessage,
    this.isApproved = true,
  });

  // 금액을 원화 형식으로 포맷
  String get formattedAmount {
    return '${amount.toString().replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    )}원';
  }

  // 카테고리 아이콘 반환
  String get categoryIcon {
    switch (category) {
      case '식음료':
        return '🍽️';
      case '교통':
        return '🚗';
      case '쇼핑':
        return '🛍️';
      case '의료':
        return '🏥';
      case '문화':
        return '🎬';
      case '기타':
      default:
        return '💳';
    }
  }

  @override
  String toString() {
    return 'Expense(id: $id, date: $date, shop: $shop, amount: $amount, category: $category)';
  }
}
